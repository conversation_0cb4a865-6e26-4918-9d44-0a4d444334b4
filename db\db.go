package db

import (
	"database/sql"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
	"log"
	"os"
)

// DBConnections 用于存储多个数据库连接
type DBConnections struct {
	MainDB *gorm.DB // 主营销数据库连接
	DataDB *gorm.DB // 数据库连接
}

var Connections DBConnections

func InitDB() {
	// 设置日志级别
	logLevel := logger.Info
	if os.Getenv("ENV") == "production" {
		logLevel = logger.Error
	}
	// 创建主数据库连接
	mainDSN := getDSN("DB_DSN", "rbcare_test:1AHV6va4begLztln@tcp(rm-wz9049tx2592u4e6pho.mysql.rds.aliyuncs.com)/rbcare_test?charset=utf8mb4&parseTime=True&loc=Local")
	Connections.MainDB = openDatabase(mainDSN, logLevel)

	// 创建数据数据库连接
	dataDSN := getDSN("DB_DATA_DSN", "rbcare_test:1AHV6va4begLztln@tcp(rm-wz9049tx2592u4e6pho.mysql.rds.aliyuncs.com)/rbcare_test?charset=utf8mb4&parseTime=True&loc=Local")
	Connections.DataDB = openDatabase(dataDSN, logLevel)
}

// 从环境变量获取 DSN，若不存在则使用默认值
func getDSN(envVar, defaultDSN string) string {
	dsn := os.Getenv(envVar)
	if dsn != "" {
		return dsn
	}
	if os.Getenv("ENV") == "production" || os.Getenv("ENV") == "test" {
		log.Fatalf("%s environment variable is not set", envVar)
	}
	return defaultDSN
}

// 打开数据库连接并初始化 GORM
func openDatabase(dsn string, logLevel logger.LogLevel) *gorm.DB {
	sqlDB, err := sql.Open("mysql", dsn)
	if err != nil {
		log.Fatalf("failed to connect to database: %v", err)
	}

	db, err := gorm.Open(mysql.New(mysql.Config{
		Conn: sqlDB,
	}), &gorm.Config{
		Logger: logger.Default.LogMode(logLevel),
	})
	if err != nil {
		log.Fatalf("failed to initialize gorm: %v", err)
	}

	return db
}
