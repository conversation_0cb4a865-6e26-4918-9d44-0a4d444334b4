package main

import (
	"github.com/robfig/cron/v3"
	"log"
	"marketing-task/job"
	"time"
)

type Job struct {
	JobName     string
	JobFunction func()
	EntryID     cron.EntryID
	Log         bool
}

func (j *Job) Run() {
	startTime := time.Now()
	if j.Log {
		log.Printf("Starting job: %s at %s", j.JobName, startTime.Format(time.RFC3339))
	}

	j.JobFunction()

	if j.Log {
		endTime := time.Now()
		log.Printf("Finished job: %s at %s, duration: %s", j.JobName, endTime.Format(time.RFC3339), endTime.Sub(startTime))
	}

}

func initializeJob() *cron.Cron {
	c := cron.New()
	// Add a job to the scheduler
	addJob(c)
	c.Start()

	return c
}

func addJob(c *cron.Cron) {
	// 心跳检测Job 每分钟跑一次
	if entryID, err := c.AddJob("*/8 * * * *", &Job{JobName: "心跳检测Job", JobFunction: job.TestJob}); err != nil {
		log.Fatalf("Failed to add job: %v", err)
	} else {
		log.Println("Job added successfully:", entryID)
	}
	// 企微用户添加标签Job 每十分钟跑一次
	wecomTagHandler := job.NewWecomTagScheduler()
	if entryID, err := c.AddJob("*/10 * * * *", &Job{JobName: "企微用户添加标签Job", JobFunction: wecomTagHandler.SyncTagsToWecom, Log: true}); err != nil {
		log.Fatalf("Failed to add job: %v", err)
	} else {
		log.Println("Job added successfully:", entryID)
	}

}
