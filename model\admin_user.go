package model

import (
	"gorm.io/gorm"
	"time"
)

// AdminUsers 代表 admin_users 表中的用户信息。
type AdminUsers struct {
	gorm.Model
	Username      string     `json:"username" gorm:"column:username;uniqueIndex;comment:用户名"`
	Password      string     `json:"password" gorm:"column:password;comment:密码"`
	Name          string     `json:"name" gorm:"column:name;comment:姓名"`
	Avatar        *string    `json:"avatar" gorm:"column:avatar;default:null;comment:头像"`
	Phone         *string    `json:"phone" gorm:"column:phone;uniqueIndex;default:null;comment:手机号"`
	Code          *int       `json:"code" gorm:"column:code;default:null;comment:验证码"`
	QWUserID      *string    `json:"qw_userid" gorm:"column:qw_userid;default:null;comment:企业微信用户ID"`
	ExpireTime    *time.Time `json:"expire_time" gorm:"column:expire_time;default:null;comment:过期时间"`
	RememberToken *string    `json:"remember_token" gorm:"column:remember_token;default:null;comment:记住我令牌"`
	Type          int8       `json:"type" gorm:"column:type;default:-1;comment:用户类型"`
	Status        *int8      `json:"status" gorm:"column:status;default:1;comment:用户状态"`
	ActivedAt     *time.Time `json:"actived_at" gorm:"column:actived_at;default:null;comment:最后活跃时间"`
	ResourceID    uint       `json:"resource_id" gorm:"column:resource_id;default:null;comment:资源ID"`
	CreatedAt     time.Time  `json:"created_at"`
	UpdatedAt     time.Time  `json:"updated_at"`
}

// TableName 返回与 AdminUsers 对应的数据库表名。
func (AdminUsers) TableName() string {
	return "admin_users"
}
