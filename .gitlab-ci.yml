image: docker:git

stages:
  - build
  - deploy
  - config


variables:
  TEST_DEPLOYMENT: marketing-task-test
  TEST_CONTAINER: marketing-task-test
  TEST_CONFIG_ENV: test
  PROD_DEPLOYMENT: marketing-task
  PROD_CONTAINER: marketing-task
  PROD_CONFIG_ENV: release
  APP_PORT: 8089
  ALI_DOCKER_REGISTRY_HOST: default-registry-vpc.cn-shenzhen.cr.aliyuncs.com
  ALI_DOCKER_REGISTRY_NAMESPACE: readboy_zs
  ALI_DOCKER_IMAGE_NAME: marketing-task
  ALI_DOCKER_IMAGE: ${ALI_DOCKER_REGISTRY_HOST}/${ALI_DOCKER_REGISTRY_NAMESPACE}/${ALI_DOCKER_IMAGE_NAME}
  K8S_NAMESPACE_TEST: gitlab
  K8S_NAMESPACE: production

build-test:
  stage: build
  script:
    - docker login -u $ALI_DOCKER_REGISTRY_USER -p $ALI_DOCKER_REGISTRY_PWD $ALI_DOCKER_REGISTRY_HOST
    - docker build --build-arg APP_ROOT=/go/src/${CI_PROJECT_NAME}
      --build-arg EXPOSE_PORT=${APP_PORT}
      --build-arg EXPOSE_PORT1=${APP_PORT1}
      --build-arg APP_NAME=${CI_PROJECT_NAME}
      --build-arg CI_PROJECT_ID=${CI_PROJECT_ID}
      --build-arg CI_PROJECT_TITLE=${CI_PROJECT_TITLE}
      --build-arg CI_PROJECT_NAME=${CI_PROJECT_NAME}
      -t $ALI_DOCKER_IMAGE:test -f Dockerfile .
    - docker push $ALI_DOCKER_IMAGE:test
  only:
    - test

deploy_test:
  stage: deploy
  variables:
    IMAGE_NAME: $ALI_DOCKER_IMAGE:test
  image: ${ALI_DOCKER_REGISTRY_HOST}/${ALI_DOCKER_REGISTRY_NAMESPACE}/kubectl:1.25.0-alpine
  before_script:
    - mkdir -p ~/.kube
    - echo $RD_KUBE_CONFIG_TEST | base64 -d > ~/.kube/config
  script:
    - echo $IMAGE_NAME
    - echo $K8S_NAMESPACE_TEST
    - echo $TEST_DEPLOYMENT
    - echo $TEST_CONTAINER
    - set -x
    - KUBECONFIG=~/.kube/config kubectl -n $K8S_NAMESPACE_TEST set image deployment/$TEST_DEPLOYMENT $TEST_CONTAINER=$IMAGE_NAME
    - KUBECONFIG=~/.kube/config kubectl -n $K8S_NAMESPACE_TEST rollout restart deployment/$TEST_DEPLOYMENT
  only:
    - test

build_prod:
  stage: build
  variables:
    IMAGE_NAME: $ALI_DOCKER_IMAGE:$CI_COMMIT_TAG
  before_script:
    - docker login -u $ALI_DOCKER_REGISTRY_USER -p $ALI_DOCKER_REGISTRY_PWD $ALI_DOCKER_REGISTRY_HOST
  script:
    - echo `date "+%Y%m%d%H%M%S"` > ./datetime
    - docker build --build-arg APP_ROOT=/go/src/${CI_PROJECT_NAME}
      --build-arg EXPOSE_PORT=${APP_PORT}
      --build-arg EXPOSE_PORT1=${APP_PORT1}
      --build-arg APP_NAME=${CI_PROJECT_NAME}
      --build-arg TAG_NAME=${CI_COMMIT_TAG}
      --build-arg CI_PROJECT_ID=${CI_PROJECT_ID}
      --build-arg CI_PROJECT_TITLE=${CI_PROJECT_TITLE}
      --build-arg CI_PROJECT_NAME=${CI_PROJECT_NAME}
      -t ${IMAGE_NAME} -f Dockerfile .
    - docker push ${IMAGE_NAME}
  artifacts:
    expire_in: 1 week
  only:
    - tags

deploy_prod:
  stage: deploy
  variables:
    IMAGE_NAME: $ALI_DOCKER_IMAGE:$CI_COMMIT_TAG
  image: ${ALI_DOCKER_REGISTRY_HOST}/${ALI_DOCKER_REGISTRY_NAMESPACE}/kubectl:1.25.0-alpine
  before_script:
    - mkdir -p ~/.kube
    - echo $RD_KUBE_CONFIG | base64 -d > ~/.kube/config
  script:
    - echo $IMAGE_NAME
    - echo $K8S_NAMESPACE
    - echo $PROD_DEPLOYMENT
    - echo $PROD_CONTAINER
    - set -x
    - KUBECONFIG=~/.kube/config kubectl -n $K8S_NAMESPACE set image deployment/$PROD_DEPLOYMENT $PROD_CONTAINER=${IMAGE_NAME}
    - KUBECONFIG=~/.kube/config kubectl -n $K8S_NAMESPACE rollout restart deployment/$PROD_DEPLOYMENT
  only:
    - tags