package model

import "time"

// WecomUserTag represents the wecom_user_tag table
type WecomUserTag struct {
	UserID    uint      `gorm:"column:user_id;primaryKey"`
	TagID     uint      `gorm:"column:tag_id;primaryKey"`
	CreatedAt time.Time `gorm:"column:created_at;default:CURRENT_TIMESTAMP"`
}

// TableName returns the table name of WecomUserTag
func (WecomUserTag) TableName() string {
	return "wecom_user_tag"
}
