package main

import (
	"log"
	"marketing-task/db"
	"os"
	"os/signal"
	"syscall"
)

func main() {
	log.SetOutput(os.Stdout)
	// 初始化数据库连接
	db.InitDB()

	// 调用初始化函数并启动调度器
	c := initializeJob()

	// 使用 defer 确保在程序退出时停止调度器
	defer c.Stop()

	log.Println("Scheduler started successfully.")

	// 捕获系统信号以优雅地退出程序
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	// 阻塞直至接收到信号
	<-sigChan
	log.Println("Received shutdown signal, shutting down gracefully...")
}
