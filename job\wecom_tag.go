package job

import (
	"github.com/spf13/cast"
	"gorm.io/gorm"
	"log"
	"marketing-task/db"
	"marketing-task/model"
	"marketing-task/wecom"
	"os"
)

type WecomTagScheduler interface {
	SyncTagsToWecom()
}

type wecomTag struct {
	db          *gorm.DB
	wecomClient *wecom.Client
}

func NewWecomTagScheduler() WecomTagScheduler {
	Db := db.Connections.MainDB
	CorpID := wecom.CorpID
	ContactsSecret := wecom.ContactsSecret
	if os.Getenv("ENV") == "production" {
		CorpID = os.Getenv("WECOM_CORP_ID")
		ContactsSecret = os.Getenv("WECOM_CONTACTS_SECRET")
		if CorpID == "" {
			log.Fatal("WECOM_CORP_ID environment variable is not set")
		}
		if ContactsSecret == "" {
			log.Fatal("WECOM_CONTACTS_SECRET environment variable is not set")
		}
	}
	wecomClient := wecom.NewWeComClient(CorpID, ContactsSecret)
	return &wecomTag{
		db:          Db,
		wecomClient: wecomClient,
	}
}

// SyncTagsToWecom 从本地同步标签数据到企微
// 临时定时任务
func (w *wecomTag) SyncTagsToWecom() {
	// 查出所有已经同步到企微的用户
	type User struct {
		model.AdminUsers
		Channel      string `gorm:"column:channel"`
		EndpointType int    `gorm:"column:endpoint_type"`
		Role         string `gorm:"column:role"`
		AgencyID     int    `gorm:"column:agency_id"`
	}
	var users []User
	err := w.db.Model(&model.AdminUsers{}).Joins("join user_endpoint as ue on admin_users.id = ue.uid").
		Joins("join endpoint as e on e.id = ue.endpoint").
		Joins("join agency as a on a.id = e.top_agency").
		Joins("left join wecom_user_tag as wut on wut.user_id = ue.uid").
		Where("admin_users.status = 1 and admin_users.qw_userid != '' and wut.user_id is null").
		Where("admin_users.created_at > ?", "2025-04-01 00:00:00").
		Select("admin_users.id,admin_users.name, admin_users.qw_userid, e.type as endpoint_type, ue.role,a.channel,a.id as agency_id").
		Group("admin_users.id").
		Find(&users).Error
	if err != nil {
		log.Printf("【添加企微标签失败】添加企微标签失败数据库查询,error:%v", err)
		return
	}

	if len(users) > 0 {
		for _, user := range users {
			// 调用企微接口同步标签
			var tagID int
			if user.Channel == "agency" || user.Channel == "other" {
				// 智习室
				if user.EndpointType == 6 {
					if user.Role == "assistant" {
						tagID = 28
					} else {
						tagID = 27
					}
				} else {
					if user.Role == "assistant" {
						tagID = 12
					} else {
						tagID = 11
					}
				}
			} else {
				if user.AgencyID == 621 {
					if user.Role == "assistant" {
						tagID = 28
					} else {
						tagID = 27
					}
				}
			}

			if tagID == 0 || user.QWUserID == nil || user.ID == 0 {
				continue
			}

			//调用企微接口给用户添加标签

			if err := w.wecomClient.AddTagUsers(tagID, []string{*user.QWUserID}, nil); err != nil {
				log.Printf("【添加企微标签失败】 user:%s,error:%v", user.Name, err)
				continue
			}
			tagsToAdd := &model.WecomUserTag{
				UserID: user.ID,
				TagID:  cast.ToUint(tagID),
			}
			if err := w.db.Create(&tagsToAdd).Error; err != nil {
				log.Printf("【添加企微标签DB失败】user:%s,error:%v", user.Name, err)
				continue
			} else {
				log.Printf("【添加企微标签成功】成功给用户添加企微标签:%s,tagID:%d", user.Name, tagID)
			}
		}
	} else {
		log.Println("【添加企微标签】没有需要添加标签的用户")
	}
}
